package com.taobao.wireless.orange.service.probe;

import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.taobao.eagleeye.EagleEye;
import com.taobao.wireless.orange.publish.probe.SwitchProbePublishService;
import com.taobao.wireless.orange.publish.probe.TextProbePublishService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j(topic = "probe")
public class ProbePublishJob extends JavaProcessor {
    @Autowired
    private SwitchProbePublishService switchProbePublishService;

    @Autowired
    private TextProbePublishService textProbePublishService;

    @Override
    public ProcessResult process(JobContext context) {
        try {
            // traceId, traceName, 90-入口型
            EagleEye.startTrace(null, getClass().getName(), EagleEye.TYPE_CUSTOM_TRACE);

            switchProbePublishService.publish();
            textProbePublishService.publish();

            log.info("Probe_generate success");
            return new ProcessResult(true);
        } catch (Throwable e) {
            log.error("Probe_generate error", e);
            return new ProcessResult(false, e.getMessage());
        } finally {
            EagleEye.endTrace(null);
        }
    }
}
