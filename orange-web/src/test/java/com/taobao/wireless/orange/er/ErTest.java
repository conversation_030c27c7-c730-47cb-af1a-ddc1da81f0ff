package com.taobao.wireless.orange.er;

import com.google.protobuf.MessageOrBuilder;
import com.google.protobuf.util.JsonFormat;
import com.taobao.wireless.orange.common.model.proto.ReleaseConfigProto;
import org.junit.Test;

import java.io.IOException;
import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

import static org.assertj.core.api.Assertions.*;

/**
 * 工具用于解析 ER 生成产物的合法性
 */
public class ErTest {

    private static final String COMBO_URL = "https://pre-combo.dd65024a.er.aliyun-esa.net/?ids=1f4aff2ef8934359af8e243c5295cb3f,d01168ecea924148a611cc5a3eea3807,0a66212537bb41e78dd88cd4002208bc,d02a9a7d0580424881279b184d9e3539,67b73f09242842ed8a6ba2d00308dec4";

    @Test
    public void testDeserializeErResult() {
        try {
            List<ParsedResource> resources = fetchAndParseBinaryContent(COMBO_URL);

            // 基本断言：验证HTTP请求成功并解析出资源
            assertThat(resources).as("解析结果不应为null").isNotNull();
            assertThat(resources.isEmpty()).as("应该解析出至少一个资源").isFalse();

            System.out.println("成功解析出 " + resources.size() + " 个资源");

            int protobufCount = 0;
            int textCount = 0;

            for (ParsedResource resource : resources) {
                // 验证每个资源的基本属性
                assertThat(resource.resourceId).as("资源ID不应为null").isNotNull();
                assertThat(resource.resourceId.trim().isEmpty()).as("资源ID不应为空").isFalse();
                assertThat(resource.resourceContent).as("资源内容不应为null").isNotNull();
                assertThat(resource.resourceContent.length > 0).as("资源内容长度应大于0").isTrue();

                System.out.println("resourceId: " + resource.resourceId);
                System.out.println("content length: " + resource.resourceContent.length);

                // 尝试解析为 protobuf
                try {
                    MessageOrBuilder proto = ReleaseConfigProto.parseFrom(resource.resourceContent);
                    assertThat(proto).as("Protobuf解析结果不应为null").isNotNull();

                    String jsonStr = JsonFormat.printer()
                            .includingDefaultValueFields()
                            .print(proto);
                    assertThat(jsonStr).as("JSON字符串不应为null").isNotNull();
                    assertThat(jsonStr.trim().isEmpty()).as("JSON字符串不应为空").isFalse();

                    System.out.println("✓ 成功解析为Protobuf");
                    System.out.println("Protobuf content:");
                    System.out.println(jsonStr);
                    protobufCount++;
                } catch (Exception e) {
                    System.out.println("✗ 无法解析为protobuf: " + e.getMessage());
                    // 如果不是 protobuf，尝试作为文本输出前100个字符
                    String textContent = new String(resource.resourceContent, StandardCharsets.UTF_8);
                    assertThat(textContent).as("文本内容不应为null").isNotNull();

                    System.out.println("Text content preview: " +
                            textContent.substring(0, Math.min(100, textContent.length())));
                    textCount++;
                }
                System.out.println("---");
            }

            // 验证解析结果的统计信息
            System.out.println("解析统计:");
            System.out.println("- Protobuf格式: " + protobufCount + " 个");
            System.out.println("- 文本格式: " + textCount + " 个");
            System.out.println("- 总计: " + resources.size() + " 个");

            // 可以根据预期调整这些断言
            assertThat((protobufCount + textCount) > 0).as("应该至少有一个资源被成功解析").isTrue();

        } catch (IOException e) {
            fail("HTTP请求或解析时发生错误: " + e.getMessage());
        } catch (IllegalArgumentException e) {
            fail("内容解析失败: " + e.getMessage());
        } catch (Exception e) {
            fail("请求失败: " + e.getMessage());
        }
    }

    @Test
    public void testHttpRequestFunctionality() {
        try {
            // 测试HTTP请求方法的基本功能
            List<ParsedResource> resources = fetchAndParseBinaryContent(COMBO_URL);

            // 验证HTTP请求成功
            assertThat(resources).as("HTTP请求结果不应为null").isNotNull();
            System.out.println("✓ HTTP请求成功");

            // 验证URL格式
            assertThat(COMBO_URL.startsWith("https://")).as("URL应该是HTTPS协议").isTrue();
            assertThat(COMBO_URL.contains("ids=")).as("URL应该包含ids参数").isTrue();
            System.out.println("✓ URL格式验证通过");

            // 验证解析出的资源数量合理
            assertThat(resources.size() >= 1 && resources.size() <= 10).as("应该解析出合理数量的资源").isTrue();
            System.out.println("✓ 解析出 " + resources.size() + " 个资源，数量合理");

            // 验证每个资源的完整性
            for (int i = 0; i < resources.size(); i++) {
                ParsedResource resource = resources.get(i);
                assertThat(resource).as("第" + (i + 1) + "个资源不应为null").isNotNull();
                assertThat(resource.resourceId).as("第" + (i + 1) + "个资源的ID不应为null").isNotNull();
                assertThat(resource.resourceContent).as("第" + (i + 1) + "个资源的内容不应为null").isNotNull();
                assertThat(resource.resourceContent.length > 0).as("第" + (i + 1) + "个资源的内容长度应大于0").isTrue();

                // 验证资源ID格式（应该是32位十六进制字符串）
                assertThat(resource.resourceId.matches("[a-f0-9]{32}")).as("资源ID应该是32位十六进制字符串: " + resource.resourceId).isTrue();
            }
            System.out.println("✓ 所有资源完整性验证通过");

        } catch (IOException e) {
            fail("HTTP请求测试失败: " + e.getMessage());
        } catch (Exception e) {
            fail("测试异常: " + e.getMessage());
        }
    }

    @Test
    public void testBinaryContentParsingEdgeCases() {
        // 测试空内容
        try {
            List<ParsedResource> emptyResult = parseBinaryContent(new byte[0]);
            assertThat(emptyResult).as("空内容解析结果不应为null").isNotNull();
            assertThat(emptyResult).as("空内容应该返回空列表").isEmpty();
            System.out.println("✓ 空内容处理正确");
        } catch (Exception e) {
            fail("空内容处理失败: " + e.getMessage());
        }

        // 测试无效格式内容
        try {
            byte[] invalidContent = "invalid binary content".getBytes(StandardCharsets.UTF_8);
            parseBinaryContent(invalidContent);
            // 如果没有抛出异常，说明处理了无效内容（可能返回空列表）
            System.out.println("✓ 无效内容处理正确");
        } catch (IllegalArgumentException e) {
            // 预期的异常
            System.out.println("✓ 无效内容正确抛出异常: " + e.getMessage());
        } catch (Exception e) {
            fail("无效内容处理异常: " + e.getMessage());
        }

        // 测试null输入
        try {
            parseBinaryContent(null);
            fail("null输入应该抛出异常");
        } catch (NullPointerException | IllegalArgumentException e) {
            System.out.println("✓ null输入正确抛出异常: " + e.getMessage());
        } catch (Exception e) {
            fail("null输入处理异常: " + e.getMessage());
        }
    }

    /**
     * 通过HTTP请求获取并解析二进制内容
     *
     * @param url 请求URL
     * @return 解析出的资源列表
     * @throws IOException              如果HTTP请求失败
     * @throws IllegalArgumentException 如果解析失败
     */
    public static List<ParsedResource> fetchAndParseBinaryContent(String url) throws IOException {
        // 使用RestTemplate进行HTTP请求
        org.springframework.web.client.RestTemplate restTemplate = new org.springframework.web.client.RestTemplate();

        try {
            // 发送GET请求获取二进制数据
            org.springframework.http.ResponseEntity<byte[]> response = restTemplate.exchange(
                    url,
                    org.springframework.http.HttpMethod.GET,
                    null,
                    byte[].class
            );

            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                byte[] responseBody = response.getBody();
                System.out.println("HTTP请求成功，获取到 " + responseBody.length + " 字节数据");
                return parseBinaryContent(responseBody);
            } else {
                throw new IOException("HTTP请求失败，状态码: " + response.getStatusCode());
            }
        } catch (Exception e) {
            throw new IOException("HTTP请求异常: " + e.getMessage(), e);
        }
    }

    /**
     * 解析新格式的二进制内容
     * 新格式：${长度}${文件ID}${长度}${文件内容}${长度}${文件ID}...
     * 长度字段定长32位（4字节），大端序
     *
     * @param contentBytes 二进制内容字节数组
     * @return 解析出的资源列表
     * @throws IllegalArgumentException 如果解析失败
     */
    public static List<ParsedResource> parseBinaryContent(byte[] contentBytes) throws IllegalArgumentException {
        List<ParsedResource> resources = new ArrayList<>();

        int offset = 0;
        while (offset < contentBytes.length) {
            // 检查是否还有足够的字节读取长度字段
            if (offset + 4 > contentBytes.length) {
                break;
            }

            // 读取文件ID长度（4字节，大端序）
            int idLength = ByteBuffer.wrap(contentBytes, offset, 4).getInt();
            offset += 4;

            // 检查是否有足够的字节读取文件ID
            if (offset + idLength > contentBytes.length) {
                throw new IllegalArgumentException("内容格式错误：文件ID长度超出范围");
            }

            // 读取文件ID
            byte[] idBytes = new byte[idLength];
            System.arraycopy(contentBytes, offset, idBytes, 0, idLength);
            String resourceId = new String(idBytes, StandardCharsets.UTF_8);
            offset += idLength;

            // 检查是否还有足够的字节读取内容长度字段
            if (offset + 4 > contentBytes.length) {
                throw new IllegalArgumentException("内容格式错误：缺少内容长度字段");
            }

            // 读取文件内容长度（4字节，大端序）
            int resourceContentLength = ByteBuffer.wrap(contentBytes, offset, 4).getInt();
            offset += 4;

            // 检查是否有足够的字节读取文件内容
            if (offset + resourceContentLength > contentBytes.length) {
                throw new IllegalArgumentException("内容格式错误：文件内容长度超出范围");
            }

            // 读取文件内容
            byte[] resourceContentBytes = new byte[resourceContentLength];
            System.arraycopy(contentBytes, offset, resourceContentBytes, 0, resourceContentLength);
            offset += resourceContentLength;

            resources.add(new ParsedResource(resourceId, resourceContentBytes));
        }

        return resources;
    }

    /**
     * 解析结果类
     */
    public record ParsedResource(String resourceId, byte[] resourceContent) {
    }
}